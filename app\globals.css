@tailwind base;
@tailwind components;
@tailwind utilities;

@layer base {
  :root {
    /* Custom color palette based on provided colors - Enhanced with brighter tones */
    /* #FDFBEE - Light cream background */
    --background: 48 85% 98%;
    --foreground: 180 60% 25%;

    /* Cards with vibrant cream tint */
    --card: 48 70% 99%;
    --card-foreground: 180 70% 30%;

    /* Popovers */
    --popover: 48 70% 99%;
    --popover-foreground: 180 70% 30%;

    /* #57B4BA - Brighter teal as primary */
    --primary: 184 45% 60%;
    --primary-foreground: 48 85% 98%;

    /* #015551 - Lighter dark teal as secondary */
    --secondary: 178 80% 25%;
    --secondary-foreground: 48 85% 98%;

    /* Brighter muted variations */
    --muted: 48 40% 95%;
    --muted-foreground: 180 30% 50%;

    /* #FE4F2D - Vibrant orange as accent */
    --accent: 11 95% 65%;
    --accent-foreground: 48 85% 98%;

    /* Destructive using vibrant orange tone */
    --destructive: 11 90% 65%;
    --destructive-foreground: 48 85% 98%;

    /* Lighter borders and inputs */
    --border: 48 30% 88%;
    --input: 48 40% 95%;
    --ring: 184 45% 60%;
    --radius: 0.75rem;

    /* Vibrant chart colors */
    --chart-1: 184 50% 65%;
    --chart-2: 178 70% 35%;
    --chart-3: 11 90% 65%;
    --chart-4: 48 80% 75%;
    --chart-5: 184 60% 45%;

    /* Additional bright accent colors */
    --success: 142 76% 55%;
    --success-foreground: 48 85% 98%;
    --warning: 45 93% 65%;
    --warning-foreground: 48 85% 98%;
    --info: 200 85% 65%;
    --info-foreground: 48 85% 98%;
  }

  .dark {
    /* Enhanced dark mode with brighter accents */
    --background: 180 60% 12%;
    --foreground: 48 85% 95%;

    /* Brighter dark cards */
    --card: 180 50% 16%;
    --card-foreground: 48 85% 95%;

    /* Enhanced dark popovers */
    --popover: 180 50% 16%;
    --popover-foreground: 48 85% 95%;

    /* Vibrant teal primary in dark mode */
    --primary: 184 55% 65%;
    --primary-foreground: 180 60% 12%;

    /* Brighter secondary */
    --secondary: 180 70% 30%;
    --secondary-foreground: 48 85% 95%;

    /* Enhanced dark muted */
    --muted: 180 30% 20%;
    --muted-foreground: 48 40% 75%;

    /* Bright orange accent in dark mode */
    --accent: 11 95% 70%;
    --accent-foreground: 180 60% 12%;

    /* Vibrant destructive in dark mode */
    --destructive: 11 85% 60%;
    --destructive-foreground: 48 85% 95%;

    /* Lighter dark borders and inputs */
    --border: 180 30% 30%;
    --input: 180 30% 25%;
    --ring: 184 55% 65%;

    /* Vibrant dark chart colors */
    --chart-1: 184 60% 70%;
    --chart-2: 178 70% 40%;
    --chart-3: 11 90% 70%;
    --chart-4: 48 70% 60%;
    --chart-5: 184 50% 50%;

    /* Additional bright colors for dark mode */
    --success: 142 70% 60%;
    --success-foreground: 180 60% 12%;
    --warning: 45 90% 70%;
    --warning-foreground: 180 60% 12%;
    --info: 200 80% 70%;
    --info-foreground: 180 60% 12%;
  }
}

@layer base {
  * {
    @apply border-border;
  }
  body {
    @apply bg-background text-foreground;
  }
}

/* Enhanced gradient backgrounds and effects with vibrant colors */
@layer components {
  .gradient-bg-primary {
    background: linear-gradient(135deg,
      hsl(var(--background)) 0%,
      hsl(184 40% 92%) 25%,
      hsl(48 60% 95%) 50%,
      hsl(184 30% 88%) 75%,
      hsl(var(--background)) 100%);
  }

  .gradient-bg-hero {
    background: linear-gradient(135deg,
      hsl(48 85% 98%) 0%,
      hsl(184 50% 88%) 15%,
      hsl(11 70% 85%) 30%,
      hsl(184 45% 75%) 45%,
      hsl(178 60% 65%) 60%,
      hsl(11 80% 75%) 75%,
      hsl(184 40% 70%) 90%,
      hsl(178 70% 35%) 100%);
    animation: gradient-shift 8s ease-in-out infinite;
  }

  @keyframes gradient-shift {
    0%, 100% { background-position: 0% 50%; }
    50% { background-position: 100% 50%; }
  }

  .card-hover {
    transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
    border: 1px solid hsl(var(--border));
    background: linear-gradient(145deg, hsl(var(--card)) 0%, hsl(48 60% 97%) 100%);
  }

  .card-hover:hover {
    transform: translateY(-6px) scale(1.02);
    box-shadow:
      0 25px 50px -12px rgba(87, 180, 186, 0.25),
      0 20px 25px -5px rgba(254, 79, 45, 0.1),
      0 10px 10px -5px rgba(0, 0, 0, 0.04);
    border-color: hsl(var(--primary));
    background: linear-gradient(145deg, hsl(48 70% 98%) 0%, hsl(184 30% 95%) 100%);
  }

  .glass-effect {
    backdrop-filter: blur(15px);
    background: linear-gradient(145deg,
      rgba(253, 251, 238, 0.9) 0%,
      rgba(87, 180, 186, 0.1) 50%,
      rgba(254, 79, 45, 0.05) 100%);
    border: 1px solid rgba(87, 180, 186, 0.3);
    box-shadow: 0 8px 32px rgba(87, 180, 186, 0.1);
  }

  .dark .glass-effect {
    background: linear-gradient(145deg,
      rgba(1, 85, 81, 0.9) 0%,
      rgba(87, 180, 186, 0.2) 50%,
      rgba(254, 79, 45, 0.1) 100%);
    border: 1px solid rgba(87, 180, 186, 0.4);
    box-shadow: 0 8px 32px rgba(87, 180, 186, 0.2);
  }

  .feature-icon {
    transition: all 0.3s ease;
  }

  .feature-icon:hover {
    transform: scale(1.1) rotate(5deg);
  }

  .btn-primary-gradient {
    background: linear-gradient(135deg,
      hsl(var(--primary)) 0%,
      hsl(184 60% 70%) 50%,
      hsl(178 70% 35%) 100%);
    border: none;
    color: hsl(var(--primary-foreground));
    transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
    box-shadow: 0 4px 15px rgba(87, 180, 186, 0.2);
  }

  .btn-primary-gradient:hover {
    background: linear-gradient(135deg,
      hsl(178 70% 35%) 0%,
      hsl(184 60% 70%) 50%,
      hsl(var(--primary)) 100%);
    transform: translateY(-3px) scale(1.05);
    box-shadow:
      0 15px 35px rgba(87, 180, 186, 0.4),
      0 5px 15px rgba(87, 180, 186, 0.2);
  }

  .btn-accent-gradient {
    background: linear-gradient(135deg,
      hsl(var(--accent)) 0%,
      hsl(11 90% 70%) 50%,
      hsl(11 80% 55%) 100%);
    border: none;
    color: hsl(var(--accent-foreground));
    transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
    box-shadow: 0 4px 15px rgba(254, 79, 45, 0.2);
  }

  .btn-accent-gradient:hover {
    background: linear-gradient(135deg,
      hsl(11 80% 55%) 0%,
      hsl(11 90% 70%) 50%,
      hsl(var(--accent)) 100%);
    transform: translateY(-3px) scale(1.05);
    box-shadow:
      0 15px 35px rgba(254, 79, 45, 0.4),
      0 5px 15px rgba(254, 79, 45, 0.2);
  }

  /* Animated background patterns */
  .animated-bg {
    background-image:
      radial-gradient(circle at 20% 80%, rgba(87, 180, 186, 0.1) 0%, transparent 50%),
      radial-gradient(circle at 80% 20%, rgba(1, 85, 81, 0.1) 0%, transparent 50%),
      radial-gradient(circle at 40% 40%, rgba(254, 79, 45, 0.05) 0%, transparent 50%);
    animation: float 6s ease-in-out infinite;
  }

  @keyframes float {
    0%, 100% { transform: translateY(0px); }
    50% { transform: translateY(-10px); }
  }

  /* Pulse animation for icons */
  .pulse-icon {
    animation: pulse 2s infinite;
  }

  @keyframes pulse {
    0%, 100% { opacity: 1; }
    50% { opacity: 0.7; }
  }

  /* Shimmer effect */
  .shimmer {
    background: linear-gradient(90deg, transparent, rgba(255,255,255,0.2), transparent);
    background-size: 200% 100%;
    animation: shimmer 2s infinite;
  }

  @keyframes shimmer {
    0% { background-position: -200% 0; }
    100% { background-position: 200% 0; }
  }

  /* Smooth transitions for all interactive elements */
  .smooth-transition {
    transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
  }

  /* Colorful utility classes */
  .bg-success {
    background-color: hsl(var(--success));
    color: hsl(var(--success-foreground));
  }

  .bg-warning {
    background-color: hsl(var(--warning));
    color: hsl(var(--warning-foreground));
  }

  .bg-info {
    background-color: hsl(var(--info));
    color: hsl(var(--info-foreground));
  }

  .text-success {
    color: hsl(var(--success));
  }

  .text-warning {
    color: hsl(var(--warning));
  }

  .text-info {
    color: hsl(var(--info));
  }

  /* Colorful gradient backgrounds */
  .bg-gradient-success {
    background: linear-gradient(135deg, hsl(var(--success)) 0%, hsl(142 60% 45%) 100%);
  }

  .bg-gradient-warning {
    background: linear-gradient(135deg, hsl(var(--warning)) 0%, hsl(45 80% 55%) 100%);
  }

  .bg-gradient-info {
    background: linear-gradient(135deg, hsl(var(--info)) 0%, hsl(200 70% 55%) 100%);
  }

  /* Enhanced icon containers */
  .icon-container-primary {
    background: linear-gradient(135deg, hsl(var(--primary)/0.1) 0%, hsl(var(--primary)/0.2) 100%);
    border: 1px solid hsl(var(--primary)/0.2);
  }

  .icon-container-accent {
    background: linear-gradient(135deg, hsl(var(--accent)/0.1) 0%, hsl(var(--accent)/0.2) 100%);
    border: 1px solid hsl(var(--accent)/0.2);
  }

  .icon-container-success {
    background: linear-gradient(135deg, hsl(var(--success)/0.1) 0%, hsl(var(--success)/0.2) 100%);
    border: 1px solid hsl(var(--success)/0.2);
  }

  .icon-container-warning {
    background: linear-gradient(135deg, hsl(var(--warning)/0.1) 0%, hsl(var(--warning)/0.2) 100%);
    border: 1px solid hsl(var(--warning)/0.2);
  }

  /* Enhanced form elements */
  .form-input-enhanced {
    background: linear-gradient(145deg, hsl(var(--input)) 0%, hsl(48 50% 97%) 100%);
    border: 2px solid hsl(var(--border));
    transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
  }

  .form-input-enhanced:focus {
    background: linear-gradient(145deg, hsl(48 60% 98%) 0%, hsl(184 20% 95%) 100%);
    border-color: hsl(var(--primary));
    box-shadow: 0 0 0 3px hsl(var(--primary)/0.1);
    transform: translateY(-1px);
  }

  /* Colorful progress bars */
  .progress-primary {
    background: linear-gradient(90deg, hsl(var(--primary)) 0%, hsl(184 60% 70%) 100%);
  }

  .progress-success {
    background: linear-gradient(90deg, hsl(var(--success)) 0%, hsl(142 60% 45%) 100%);
  }

  .progress-warning {
    background: linear-gradient(90deg, hsl(var(--warning)) 0%, hsl(45 80% 55%) 100%);
  }

  .progress-accent {
    background: linear-gradient(90deg, hsl(var(--accent)) 0%, hsl(11 80% 55%) 100%);
  }
}
