"use client"

import type React from "react"

import { useState } from "react"
import { <PERSON><PERSON> } from "@/components/ui/button"
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from "@/components/ui/card"
import { Input } from "@/components/ui/input"
import { Label } from "@/components/ui/label"
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from "@/components/ui/select"
import { <PERSON><PERSON>, TabsContent, TabsList, TabsTrigger } from "@/components/ui/tabs"
import { Badge } from "@/components/ui/badge"
import { ThemeToggle } from "@/components/theme-toggle"
import { Users, Award, TrendingUp, Heart } from "lucide-react"

export default function HomePage() {
  const [loginData, setLoginData] = useState({ email: "", password: "", role: "" })
  const [registerData, setRegisterData] = useState({
    name: "",
    email: "",
    password: "",
    role: "",
    center: "",
  })

  const handleLogin = (e: React.FormEvent) => {
    e.preventDefault()

    // Simple demo authentication
    if (!loginData.email || !loginData.password || !loginData.role) {
      alert("Please fill in all fields")
      return
    }

    // Mock user data
    const user = {
      id: "1",
      name: loginData.email.split("@")[0].charAt(0).toUpperCase() + loginData.email.split("@")[0].slice(1),
      email: loginData.email,
      role: loginData.role,
      center: "Main Center",
    }

    localStorage.setItem("user", JSON.stringify(user))

    // Redirect based on role
    const dashboardRoutes = {
      admin: "/admin",
      teacher: "/teacher",
      student: "/student",
      parent: "/parent",
    }

    window.location.href = dashboardRoutes[loginData.role as keyof typeof dashboardRoutes] || "/student"
  }

  const handleRegister = (e: React.FormEvent) => {
    e.preventDefault()
    // Mock registration
    alert("Registration successful! Please login.")
  }

  return (
    <div className="min-h-screen gradient-bg-hero animated-bg">
      {/* Theme Toggle */}
      <div className="absolute top-4 right-4 z-10">
        <ThemeToggle />
      </div>

      {/* Hero Section */}
      <div className="container mx-auto px-4 py-12">
        <div className="text-center mb-16">
          <div className="flex items-center justify-center gap-3 mb-6">
            <div className="p-3 rounded-full bg-white/20 backdrop-blur-sm border border-white/30 shimmer">
              <Heart className="h-10 w-10 text-accent feature-icon pulse-icon" />
            </div>
            <h1 className="text-5xl font-bold bg-gradient-to-r from-primary to-secondary bg-clip-text text-transparent smooth-transition hover:scale-105">
              Mentor's Mark
            </h1>
          </div>
          <p className="text-2xl text-secondary/80 mb-4 font-medium">NGO Student Growth Tracker</p>
          <p className="text-lg text-muted-foreground mb-12 max-w-2xl mx-auto">
            Empowering education through gamified learning experiences and comprehensive progress tracking
          </p>

          <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-8 mb-16">
            <Card className="text-center card-hover glass-effect border-0">
              <CardContent className="pt-8 pb-6">
                <div className="p-4 rounded-full icon-container-primary w-fit mx-auto mb-4">
                  <Users className="h-12 w-12 text-primary feature-icon" />
                </div>
                <h3 className="font-bold text-lg mb-2 text-secondary">Multi-Role Access</h3>
                <p className="text-sm text-muted-foreground leading-relaxed">
                  Seamless access for Admin, Teacher, Student, and Parent roles
                </p>
              </CardContent>
            </Card>

            <Card className="text-center card-hover glass-effect border-0">
              <CardContent className="pt-8 pb-6">
                <div className="p-4 rounded-full icon-container-warning w-fit mx-auto mb-4">
                  <Award className="h-12 w-12 text-warning feature-icon" />
                </div>
                <h3 className="font-bold text-lg mb-2 text-secondary">Gamification</h3>
                <p className="text-sm text-muted-foreground leading-relaxed">
                  Engaging XP Points, Achievement Badges, and Dynamic Leaderboards
                </p>
              </CardContent>
            </Card>

            <Card className="text-center card-hover glass-effect border-0">
              <CardContent className="pt-8 pb-6">
                <div className="p-4 rounded-full icon-container-success w-fit mx-auto mb-4">
                  <TrendingUp className="h-12 w-12 text-success feature-icon" />
                </div>
                <h3 className="font-bold text-lg mb-2 text-secondary">Progress Tracking</h3>
                <p className="text-sm text-muted-foreground leading-relaxed">
                  Comprehensive monitoring of Behavior, Academics, and Attendance
                </p>
              </CardContent>
            </Card>

            <Card className="text-center card-hover glass-effect border-0">
              <CardContent className="pt-8 pb-6">
                <div className="p-4 rounded-full icon-container-accent w-fit mx-auto mb-4">
                  <Heart className="h-12 w-12 text-accent feature-icon" />
                </div>
                <h3 className="font-bold text-lg mb-2 text-secondary">Impact Analytics</h3>
                <p className="text-sm text-muted-foreground leading-relaxed">
                  Detailed Reports, Trend Analysis, and Actionable Insights
                </p>
              </CardContent>
            </Card>
          </div>
        </div>

        {/* Authentication */}
        <div className="max-w-lg mx-auto">
          <Card className="glass-effect border-0 shadow-2xl">
            <CardHeader className="text-center pb-2">
              <CardTitle className="text-2xl font-bold text-secondary">Welcome to Mentor's Mark</CardTitle>
              <CardDescription className="text-muted-foreground text-base">
                Login or register to begin your educational journey
              </CardDescription>
            </CardHeader>
            <CardContent className="pt-6">
              <Tabs defaultValue="login" className="w-full">
                <TabsList className="grid w-full grid-cols-2 bg-muted/50 p-1 rounded-lg">
                  <TabsTrigger
                    value="login"
                    className="data-[state=active]:bg-primary data-[state=active]:text-primary-foreground font-medium"
                  >
                    Login
                  </TabsTrigger>
                  <TabsTrigger
                    value="register"
                    className="data-[state=active]:bg-primary data-[state=active]:text-primary-foreground font-medium"
                  >
                    Register
                  </TabsTrigger>
                </TabsList>

                <TabsContent value="login" className="mt-6">
                  <form onSubmit={handleLogin} className="space-y-6">
                    <div className="space-y-2">
                      <Label htmlFor="email" className="text-secondary font-medium">Email Address</Label>
                      <Input
                        id="email"
                        type="email"
                        placeholder="Enter your email"
                        value={loginData.email}
                        onChange={(e) => setLoginData({ ...loginData, email: e.target.value })}
                        className="h-12 form-input-enhanced"
                        required
                      />
                    </div>

                    <div className="space-y-2">
                      <Label htmlFor="password" className="text-secondary font-medium">Password</Label>
                      <Input
                        id="password"
                        type="password"
                        placeholder="Enter your password"
                        value={loginData.password}
                        onChange={(e) => setLoginData({ ...loginData, password: e.target.value })}
                        className="h-12 form-input-enhanced"
                        required
                      />
                    </div>

                    <div className="space-y-2">
                      <Label htmlFor="role" className="text-secondary font-medium">Select Role</Label>
                      <Select
                        value={loginData.role}
                        onValueChange={(value) => setLoginData({ ...loginData, role: value })}
                      >
                        <SelectTrigger className="h-12 form-input-enhanced">
                          <SelectValue placeholder="Choose your role" />
                        </SelectTrigger>
                        <SelectContent>
                          <SelectItem value="admin">Admin</SelectItem>
                          <SelectItem value="teacher">Teacher/Volunteer</SelectItem>
                          <SelectItem value="student">Student</SelectItem>
                          <SelectItem value="parent">Parent</SelectItem>
                        </SelectContent>
                      </Select>
                    </div>

                    <Button type="submit" className="w-full h-12 btn-primary-gradient text-lg font-semibold">
                      Sign In to Mentor's Mark
                    </Button>
                  </form>

                  <div className="mt-6 p-6 glass-effect rounded-xl border border-border/50">
                    <p className="text-sm font-semibold mb-4 text-secondary flex items-center gap-2">
                      <span className="w-2 h-2 bg-accent rounded-full animate-pulse"></span>
                      Demo Credentials
                    </p>
                    <div className="grid grid-cols-2 gap-3 text-xs">
                      <div className="flex items-center gap-2">
                        <Badge className="bg-gradient-to-r from-primary to-primary/80 text-primary-foreground border-0 shadow-sm">Admin</Badge>
                        <span className="text-muted-foreground"><EMAIL></span>
                      </div>
                      <div className="flex items-center gap-2">
                        <Badge className="bg-gradient-to-r from-accent to-accent/80 text-accent-foreground border-0 shadow-sm">Teacher</Badge>
                        <span className="text-muted-foreground"><EMAIL></span>
                      </div>
                      <div className="flex items-center gap-2">
                        <Badge className="bg-gradient-success text-success-foreground border-0 shadow-sm">Student</Badge>
                        <span className="text-muted-foreground"><EMAIL></span>
                      </div>
                      <div className="flex items-center gap-2">
                        <Badge className="bg-gradient-info text-info-foreground border-0 shadow-sm">Parent</Badge>
                        <span className="text-muted-foreground"><EMAIL></span>
                      </div>
                    </div>
                    <p className="text-xs text-muted-foreground mt-4 text-center">
                      Password: <span className="font-mono bg-primary/10 text-primary px-3 py-1 rounded-full">password</span>
                    </p>
                  </div>
                </TabsContent>

                <TabsContent value="register" className="mt-6">
                  <form onSubmit={handleRegister} className="space-y-5">
                    <div className="space-y-2">
                      <Label htmlFor="name" className="text-secondary font-medium">Full Name</Label>
                      <Input
                        id="name"
                        placeholder="Enter your full name"
                        value={registerData.name}
                        onChange={(e) => setRegisterData({ ...registerData, name: e.target.value })}
                        className="h-11 border-2 border-border focus:border-primary transition-colors"
                        required
                      />
                    </div>

                    <div className="space-y-2">
                      <Label htmlFor="reg-email" className="text-secondary font-medium">Email Address</Label>
                      <Input
                        id="reg-email"
                        type="email"
                        placeholder="Enter your email"
                        value={registerData.email}
                        onChange={(e) => setRegisterData({ ...registerData, email: e.target.value })}
                        className="h-11 border-2 border-border focus:border-primary transition-colors"
                        required
                      />
                    </div>

                    <div className="space-y-2">
                      <Label htmlFor="reg-password" className="text-secondary font-medium">Password</Label>
                      <Input
                        id="reg-password"
                        type="password"
                        placeholder="Create a password"
                        value={registerData.password}
                        onChange={(e) => setRegisterData({ ...registerData, password: e.target.value })}
                        className="h-11 border-2 border-border focus:border-primary transition-colors"
                        required
                      />
                    </div>

                    <div className="grid grid-cols-2 gap-4">
                      <div className="space-y-2">
                        <Label htmlFor="reg-role" className="text-secondary font-medium">Role</Label>
                        <Select
                          value={registerData.role}
                          onValueChange={(value) => setRegisterData({ ...registerData, role: value })}
                        >
                          <SelectTrigger className="h-11 border-2 border-border focus:border-primary">
                            <SelectValue placeholder="Select role" />
                          </SelectTrigger>
                          <SelectContent>
                            <SelectItem value="teacher">Teacher/Volunteer</SelectItem>
                            <SelectItem value="student">Student</SelectItem>
                            <SelectItem value="parent">Parent</SelectItem>
                          </SelectContent>
                        </Select>
                      </div>

                      <div className="space-y-2">
                        <Label htmlFor="center" className="text-secondary font-medium">Center</Label>
                        <Select
                          value={registerData.center}
                          onValueChange={(value) => setRegisterData({ ...registerData, center: value })}
                        >
                          <SelectTrigger className="h-11 border-2 border-border focus:border-primary">
                            <SelectValue placeholder="Select center" />
                          </SelectTrigger>
                          <SelectContent>
                            <SelectItem value="main">Main Center</SelectItem>
                            <SelectItem value="north">North Branch</SelectItem>
                            <SelectItem value="south">South Branch</SelectItem>
                            <SelectItem value="east">East Branch</SelectItem>
                          </SelectContent>
                        </Select>
                      </div>
                    </div>

                    <Button type="submit" className="w-full h-12 btn-accent-gradient text-lg font-semibold mt-6">
                      Create Account
                    </Button>
                  </form>
                </TabsContent>
              </Tabs>
            </CardContent>
          </Card>
        </div>
      </div>
    </div>
  )
}
